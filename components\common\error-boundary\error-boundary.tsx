"use client"

import React from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { AlertTriangle, RefreshCw, Home, Bug } from 'lucide-react'
import { errorService } from '@/lib/services/error.service'
import { ApplicationError } from '@/lib/types/error.types'

interface ErrorBoundaryState {
  hasError: boolean
  error: Error | null
  errorInfo: React.ErrorInfo | null
}

interface ErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ComponentType<ErrorFallbackProps>
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void
  showErrorDetails?: boolean
}

interface ErrorFallbackProps {
  error: Error
  resetError: () => void
  showDetails?: boolean
}

/**
 * Default error fallback component
 */
function DefaultErrorFallback({ error, resetError, showDetails = false }: ErrorFallbackProps) {
  const isApplicationError = error instanceof ApplicationError
  const userMessage = isApplicationError 
    ? error.toUserFriendly() 
    : errorService.getUserFriendlyMessage(error)

  return (
    <div className="flex items-center justify-center min-h-[400px] p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-destructive/10">
            <AlertTriangle className="h-6 w-6 text-destructive" />
          </div>
          <CardTitle className="text-xl">Something went wrong</CardTitle>
          <CardDescription>
            {userMessage}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {showDetails && (
            <details className="rounded-lg bg-muted p-4">
              <summary className="cursor-pointer font-medium text-sm">
                Technical Details
              </summary>
              <div className="mt-2 text-xs font-mono text-muted-foreground">
                <p><strong>Error:</strong> {error.name}</p>
                <p><strong>Message:</strong> {error.message}</p>
                {error.stack && (
                  <div className="mt-2">
                    <strong>Stack Trace:</strong>
                    <pre className="mt-1 whitespace-pre-wrap text-xs">
                      {error.stack}
                    </pre>
                  </div>
                )}
              </div>
            </details>
          )}
          
          <div className="flex flex-col gap-2 sm:flex-row">
            <Button onClick={resetError} className="flex-1">
              <RefreshCw className="mr-2 h-4 w-4" />
              Try Again
            </Button>
            <Button 
              variant="outline" 
              onClick={() => window.location.href = '/'}
              className="flex-1"
            >
              <Home className="mr-2 h-4 w-4" />
              Go Home
            </Button>
          </div>
          
          {process.env.NODE_ENV === 'development' && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                console.error('Error Boundary caught error:', error)
                // You could also send to error reporting service here
              }}
              className="w-full"
            >
              <Bug className="mr-2 h-4 w-4" />
              Log Error (Dev)
            </Button>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

/**
 * Error boundary component that catches JavaScript errors anywhere in the child component tree
 */
export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    }
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      error,
    }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    this.setState({
      error,
      errorInfo,
    })

    // Log error to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Error Boundary caught an error:', error, errorInfo)
    }

    // Call custom error handler if provided
    this.props.onError?.(error, errorInfo)

    // Report error to error service
    errorService.handleError(error, 'React Error Boundary', {
      showToast: false, // Don't show toast as we're showing the error UI
      logError: true,
      reportError: true,
      context: {
        component: 'ErrorBoundary',
        additionalData: {
          componentStack: errorInfo.componentStack,
        },
      },
    })
  }

  resetError = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    })
  }

  render() {
    if (this.state.hasError && this.state.error) {
      const FallbackComponent = this.props.fallback || DefaultErrorFallback
      
      return (
        <FallbackComponent
          error={this.state.error}
          resetError={this.resetError}
          showDetails={this.props.showErrorDetails}
        />
      )
    }

    return this.props.children
  }
}

/**
 * Hook-based error boundary for functional components
 */
export function useErrorHandler() {
  return React.useCallback((error: Error, errorInfo?: { componentStack?: string }) => {
    // Handle the error using our error service
    errorService.handleError(error, 'Component Error Handler', {
      showToast: true,
      logError: true,
      reportError: true,
      context: {
        component: 'useErrorHandler',
        additionalData: errorInfo,
      },
    })
  }, [])
}

/**
 * Higher-order component that wraps a component with error boundary
 */
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<ErrorBoundaryProps, 'children'>
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  )

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`
  
  return WrappedComponent
}

/**
 * Async error boundary for handling promise rejections
 */
export function AsyncErrorBoundary({ 
  children, 
  fallback,
  onError 
}: {
  children: React.ReactNode
  fallback?: React.ComponentType<ErrorFallbackProps>
  onError?: (error: Error) => void
}) {
  const [error, setError] = React.useState<Error | null>(null)

  React.useEffect(() => {
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      const error = event.reason instanceof Error ? event.reason : new Error(String(event.reason))
      setError(error)
      onError?.(error)
      
      // Prevent the default browser behavior
      event.preventDefault()
    }

    window.addEventListener('unhandledrejection', handleUnhandledRejection)
    
    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection)
    }
  }, [onError])

  const resetError = React.useCallback(() => {
    setError(null)
  }, [])

  if (error) {
    const FallbackComponent = fallback || DefaultErrorFallback
    return <FallbackComponent error={error} resetError={resetError} />
  }

  return <>{children}</>
}

/**
 * Error boundary specifically for API errors
 */
export function APIErrorBoundary({ 
  children,
  onRetry
}: {
  children: React.ReactNode
  onRetry?: () => void
}) {
  return (
    <ErrorBoundary
      fallback={({ error, resetError }) => (
        <div className="flex items-center justify-center min-h-[200px] p-4">
          <Card className="w-full max-w-sm">
            <CardHeader className="text-center">
              <AlertTriangle className="mx-auto h-8 w-8 text-destructive mb-2" />
              <CardTitle className="text-lg">API Error</CardTitle>
              <CardDescription>
                {errorService.getUserFriendlyMessage(error)}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex gap-2">
                <Button 
                  onClick={() => {
                    resetError()
                    onRetry?.()
                  }} 
                  className="flex-1"
                >
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Retry
                </Button>
                <Button variant="outline" onClick={resetError} className="flex-1">
                  Dismiss
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    >
      {children}
    </ErrorBoundary>
  )
}

// Export types for external use
export type { ErrorFallbackProps, ErrorBoundaryProps }
