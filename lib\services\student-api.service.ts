import { BaseAPIService, APIRequestConfig } from './api.service'

/**
 * Student-specific data types
 */
export interface StudentDashboardData {
  stats: {
    totalQuizzes: number
    completedQuizzes: number
    averageScore: number
    totalPoints: number
    currentLevel: number
    rank: number
  }
  recentAttempts: QuizAttempt[]
  enrolledQuizzes: Quiz[]
  achievements: Achievement[]
}

export interface Quiz {
  id: string
  title: string
  description?: string
  subject?: string
  difficulty: 'EASY' | 'MEDIUM' | 'HARD'
  questionCount: number
  timeLimit?: number
  isPublished: boolean
  createdAt: string
  updatedAt: string
}

export interface QuizAttempt {
  id: string
  quizId: string
  quiz: Quiz
  startedAt: string
  completedAt?: string
  score?: number
  percentage?: number
  timeSpent?: number
  status: 'IN_PROGRESS' | 'COMPLETED' | 'PAUSED' | 'ABANDONED'
}

export interface Achievement {
  id: string
  title: string
  description: string
  icon: string
  unlockedAt: string
  points: number
}

export interface QuizQuestion {
  id: string
  type: 'MCQ' | 'TRUE_FALSE' | 'FILL_BLANK' | 'ESSAY'
  text: string
  options?: string[]
  correctAnswer?: string
  explanation?: string
  points: number
  difficulty?: 'EASY' | 'MEDIUM' | 'HARD'
  tags?: string[]
  imageUrl?: string
  order?: number
}

export interface QuizAttemptDetails {
  id: string
  attemptId: string
  quizId: string
  quiz: Quiz
  questions: QuizQuestion[]
  answers: Record<string, any>
  currentQuestionIndex: number
  timeRemaining?: number
  startedAt: string
  isPaused: boolean
  canResume: boolean
  maxAttempts: number
  attemptNumber: number
}

export interface QuizResult {
  id: string
  attemptId: string
  quiz: Quiz
  score: number
  percentage: number
  totalQuestions: number
  correctAnswers: number
  timeSpent: number
  completedAt: string
  answers: Array<{
    questionId: string
    question: QuizQuestion
    userAnswer: any
    isCorrect: boolean
    points: number
  }>
}

/**
 * Student API Service
 * Handles all student-related API calls with consistent error handling and toast notifications
 */
class StudentAPIService extends BaseAPIService {
  constructor() {
    super('/api/student')
  }

  /**
   * Dashboard APIs
   */
  async getDashboardData(): Promise<StudentDashboardData> {
    return this.get<StudentDashboardData>('/dashboard', {
      context: 'Loading dashboard data',
      showErrorToast: true,
    })
  }

  /**
   * Quiz browsing APIs
   */
  async getAvailableQuizzes(filters?: {
    subject?: string
    difficulty?: string
    search?: string
    page?: number
    limit?: number
  }): Promise<{ quizzes: Quiz[]; total: number; page: number; totalPages: number }> {
    const params = new URLSearchParams()
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== '') {
          params.append(key, value.toString())
        }
      })
    }

    const endpoint = params.toString() ? `/browse?${params.toString()}` : '/browse'
    
    return this.get(endpoint, {
      context: 'Loading available quizzes',
      showErrorToast: true,
    })
  }

  async getQuizDetails(quizId: string): Promise<Quiz> {
    return this.get<Quiz>(`/quiz/${quizId}`, {
      context: 'Loading quiz details',
      showErrorToast: true,
    })
  }

  /**
   * Quiz attempt APIs
   */
  async startQuizAttempt(quizId: string): Promise<QuizAttemptDetails> {
    return this.post<QuizAttemptDetails>(`/quiz/${quizId}/attempt`, undefined, {
      context: 'Starting quiz attempt',
      showErrorToast: true,
      showSuccessToast: true,
      successMessage: 'Quiz started successfully!',
    })
  }

  async getQuizAttempt(quizId: string, attemptId?: string): Promise<QuizAttemptDetails> {
    const endpoint = attemptId ? `/quiz/${quizId}/attempt/${attemptId}` : `/quiz/${quizId}/attempt`
    
    return this.get<QuizAttemptDetails>(endpoint, {
      context: 'Loading quiz attempt',
      showErrorToast: true,
    })
  }

  async saveQuizAnswer(
    quizId: string,
    attemptId: string,
    questionId: string,
    answer: any
  ): Promise<void> {
    return this.post(`/quiz/${quizId}/attempt/${attemptId}/answer`, {
      questionId,
      answer,
    }, {
      context: 'Saving answer',
      showErrorToast: false, // Don't show error toast for individual answer saves
    })
  }

  async pauseQuizAttempt(quizId: string, attemptId: string): Promise<void> {
    return this.post(`/quiz/${quizId}/attempt/${attemptId}/pause`, undefined, {
      context: 'Pausing quiz',
      showErrorToast: true,
      showSuccessToast: true,
      successMessage: 'Quiz paused successfully',
    })
  }

  async resumeQuizAttempt(quizId: string, attemptId: string): Promise<QuizAttemptDetails> {
    return this.post<QuizAttemptDetails>(`/quiz/${quizId}/attempt/${attemptId}/resume`, undefined, {
      context: 'Resuming quiz',
      showErrorToast: true,
      showSuccessToast: true,
      successMessage: 'Quiz resumed successfully',
    })
  }

  async submitQuizAttempt(quizId: string, attemptId: string): Promise<QuizResult> {
    return this.post<QuizResult>(`/quiz/${quizId}/attempt/${attemptId}/submit`, undefined, {
      context: 'Submitting quiz',
      showErrorToast: true,
      showSuccessToast: true,
      successMessage: 'Quiz submitted successfully!',
    })
  }

  /**
   * Quiz results APIs
   */
  async getQuizResult(quizId: string, attemptId: string): Promise<QuizResult> {
    return this.get<QuizResult>(`/quiz/${quizId}/result/${attemptId}`, {
      context: 'Loading quiz result',
      showErrorToast: true,
    })
  }

  async getQuizHistory(filters?: {
    page?: number
    limit?: number
    subject?: string
  }): Promise<{ attempts: QuizAttempt[]; total: number; page: number; totalPages: number }> {
    const params = new URLSearchParams()
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== '') {
          params.append(key, value.toString())
        }
      })
    }

    const endpoint = params.toString() ? `/history?${params.toString()}` : '/history'
    
    return this.get(endpoint, {
      context: 'Loading quiz history',
      showErrorToast: true,
    })
  }

  /**
   * Profile and achievements APIs
   */
  async getProfile(): Promise<{
    user: any
    stats: any
    achievements: Achievement[]
  }> {
    return this.get('/profile', {
      context: 'Loading profile',
      showErrorToast: true,
    })
  }

  async updateProfile(data: {
    name?: string
    bio?: string
  }): Promise<void> {
    return this.patch('/profile', data, {
      context: 'Updating profile',
      showErrorToast: true,
      showSuccessToast: true,
      successMessage: 'Profile updated successfully!',
    })
  }

  /**
   * Categories and subjects APIs
   */
  async getSubjects(): Promise<any[]> {
    return this.get('/categories/subjects', {
      context: 'Loading subjects',
      showErrorToast: true,
    })
  }

  async getChapters(subjectId: string): Promise<any[]> {
    return this.get(`/categories/subjects/${subjectId}/chapters`, {
      context: 'Loading chapters',
      showErrorToast: true,
    })
  }

  async getTopics(chapterId: string): Promise<any[]> {
    return this.get(`/categories/chapters/${chapterId}/topics`, {
      context: 'Loading topics',
      showErrorToast: true,
    })
  }

  /**
   * Practice session APIs
   */
  async startPracticeSession(config: {
    subjectId?: string
    chapterId?: string
    topicId?: string
    difficulty?: string
    questionCount?: number
  }): Promise<QuizAttemptDetails> {
    return this.post('/practice/start', config, {
      context: 'Starting practice session',
      showErrorToast: true,
      showSuccessToast: true,
      successMessage: 'Practice session started!',
    })
  }

  /**
   * Utility methods
   */
  async retryFailedRequest<T>(
    requestFn: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000
  ): Promise<T> {
    let lastError: Error

    for (let i = 0; i < maxRetries; i++) {
      try {
        return await requestFn()
      } catch (error) {
        lastError = error as Error
        
        if (i < maxRetries - 1) {
          // Wait before retrying
          await new Promise(resolve => setTimeout(resolve, delay * (i + 1)))
        }
      }
    }

    throw lastError!
  }
}

// Export singleton instance
export const studentApiService = new StudentAPIService()

// Export class for testing or custom instances
export { StudentAPIService }
