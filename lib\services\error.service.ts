import { 
  ApplicationError, 
  NetworkError, 
  ValidationError, 
  AuthenticationError, 
  AuthorizationError,
  ErrorCode, 
  ErrorSeverity, 
  ErrorContext, 
  ErrorHandlingOptions,
  APIErrorResponse 
} from '@/lib/types/error.types'
import { toastService } from './toast.service'

/**
 * User-friendly error messages mapping
 */
const USER_FRIENDLY_MESSAGES: Record<ErrorCode, string> = {
  [ErrorCode.UNAUTHORIZED]: 'Your session has expired. Please log in again.',
  [ErrorCode.FORBIDDEN]: 'You do not have permission to perform this action.',
  [ErrorCode.SESSION_EXPIRED]: 'Your session has expired. Please log in again.',
  [ErrorCode.INVALID_CREDENTIALS]: 'Invalid email or password. Please try again.',
  
  [ErrorCode.VALIDATION_ERROR]: 'Please check your input and try again.',
  [ErrorCode.INVALID_INPUT]: 'Invalid input provided. Please check your data.',
  [ErrorCode.MISSING_REQUIRED_FIELD]: 'Please fill in all required fields.',
  
  [ErrorCode.NOT_FOUND]: 'The requested resource was not found.',
  [ErrorCode.ALREADY_EXISTS]: 'This item already exists.',
  [ErrorCode.CONFLICT]: 'This action conflicts with the current state. Please refresh and try again.',
  [ErrorCode.RESOURCE_LOCKED]: 'This resource is currently locked. Please try again later.',
  
  [ErrorCode.RATE_LIMIT_EXCEEDED]: 'Too many requests. Please wait a moment before trying again.',
  [ErrorCode.QUOTA_EXCEEDED]: 'You have exceeded your quota. Please upgrade your plan.',
  [ErrorCode.MAX_ATTEMPTS_EXCEEDED]: 'Maximum attempts exceeded. Please try again later.',
  
  [ErrorCode.INTERNAL_ERROR]: 'Server error. Please try again in a moment.',
  [ErrorCode.SERVICE_UNAVAILABLE]: 'Service temporarily unavailable. Please try again later.',
  [ErrorCode.DATABASE_ERROR]: 'Database error. Please try again later.',
  [ErrorCode.EXTERNAL_SERVICE_ERROR]: 'External service error. Please try again later.',
  
  [ErrorCode.NETWORK_ERROR]: 'Network error. Please check your internet connection.',
  [ErrorCode.TIMEOUT_ERROR]: 'Request timed out. Please try again.',
  [ErrorCode.CONNECTION_ERROR]: 'Connection error. Please check your internet connection.',
  
  [ErrorCode.QUIZ_NOT_AVAILABLE]: 'Quiz is not available at this time.',
  [ErrorCode.QUIZ_ALREADY_COMPLETED]: 'You have already completed this quiz.',
  [ErrorCode.INSUFFICIENT_PERMISSIONS]: 'You do not have sufficient permissions.',
  [ErrorCode.OPERATION_NOT_ALLOWED]: 'This operation is not allowed.',
  
  [ErrorCode.FILE_TOO_LARGE]: 'File is too large. Please choose a smaller file.',
  [ErrorCode.INVALID_FILE_TYPE]: 'Invalid file type. Please choose a supported file.',
  [ErrorCode.UPLOAD_FAILED]: 'File upload failed. Please try again.',
  
  [ErrorCode.UNKNOWN_ERROR]: 'An unexpected error occurred. Please try again.',
}

class ErrorService {
  /**
   * Extract error information from any error type
   */
  private extractErrorInfo(error: any): {
    message: string
    code: ErrorCode
    statusCode: number
    severity: ErrorSeverity
  } {
    // Handle ApplicationError instances
    if (error instanceof ApplicationError) {
      return {
        message: error.message,
        code: error.code,
        statusCode: error.statusCode,
        severity: error.severity,
      }
    }

    // Handle standard Error instances
    if (error instanceof Error) {
      // Check for network errors
      if (error.message.includes('fetch') || error.message.includes('network')) {
        return {
          message: error.message,
          code: ErrorCode.NETWORK_ERROR,
          statusCode: 0,
          severity: ErrorSeverity.HIGH,
        }
      }

      return {
        message: error.message,
        code: ErrorCode.UNKNOWN_ERROR,
        statusCode: 500,
        severity: ErrorSeverity.MEDIUM,
      }
    }

    // Handle API error responses
    if (error && typeof error === 'object') {
      if ('code' in error && 'message' in error) {
        return {
          message: error.message || 'An error occurred',
          code: error.code || ErrorCode.UNKNOWN_ERROR,
          statusCode: error.statusCode || 500,
          severity: ErrorSeverity.MEDIUM,
        }
      }

      if ('error' in error) {
        return {
          message: error.error || 'An error occurred',
          code: ErrorCode.UNKNOWN_ERROR,
          statusCode: error.status || 500,
          severity: ErrorSeverity.MEDIUM,
        }
      }
    }

    // Handle string errors
    if (typeof error === 'string') {
      return {
        message: error,
        code: ErrorCode.UNKNOWN_ERROR,
        statusCode: 500,
        severity: ErrorSeverity.MEDIUM,
      }
    }

    // Fallback for unknown error types
    return {
      message: 'An unexpected error occurred',
      code: ErrorCode.UNKNOWN_ERROR,
      statusCode: 500,
      severity: ErrorSeverity.MEDIUM,
    }
  }

  /**
   * Get user-friendly error message
   */
  getUserFriendlyMessage(error: any, fallback?: string): string {
    const { message, code } = this.extractErrorInfo(error)

    // Use predefined user-friendly message if available
    if (USER_FRIENDLY_MESSAGES[code]) {
      return USER_FRIENDLY_MESSAGES[code]
    }

    // Use the error message if it seems user-friendly
    if (message && !message.includes('Error:') && !message.includes('Exception:')) {
      return message
    }

    // Use fallback or default message
    return fallback || USER_FRIENDLY_MESSAGES[ErrorCode.UNKNOWN_ERROR]
  }

  /**
   * Handle API response and throw appropriate error
   */
  async handleAPIResponse(response: Response): Promise<any> {
    if (response.ok) {
      try {
        return await response.json()
      } catch {
        // If JSON parsing fails on successful response, return empty object
        return {}
      }
    }

    let errorData: any = {}
    try {
      const text = await response.text()
      if (text) {
        try {
          errorData = JSON.parse(text)
        } catch {
          // If not valid JSON, treat as plain text message
          errorData = { message: text }
        }
      } else {
        errorData = { message: response.statusText || 'Unknown error' }
      }
    } catch {
      // If we can't read the response at all, use status text
      errorData = { message: response.statusText || 'Unknown error' }
    }

    const userFriendlyMessage = this.getUserFriendlyMessage({
      ...errorData,
      statusCode: response.status,
    })

    // Create appropriate error type based on status code
    switch (response.status) {
      case 401:
        throw new AuthenticationError(userFriendlyMessage)
      case 403:
        throw new AuthorizationError(userFriendlyMessage)
      case 400:
        if (errorData.code === ErrorCode.VALIDATION_ERROR) {
          throw new ValidationError(userFriendlyMessage, errorData.details)
        }
        throw new ApplicationError(
          userFriendlyMessage,
          errorData.code || ErrorCode.INVALID_INPUT,
          400,
          ErrorSeverity.LOW
        )
      case 404:
        throw new ApplicationError(
          userFriendlyMessage,
          ErrorCode.NOT_FOUND,
          404,
          ErrorSeverity.LOW
        )
      case 409:
        throw new ApplicationError(
          userFriendlyMessage,
          ErrorCode.CONFLICT,
          409,
          ErrorSeverity.MEDIUM
        )
      case 429:
        throw new ApplicationError(
          userFriendlyMessage,
          ErrorCode.RATE_LIMIT_EXCEEDED,
          429,
          ErrorSeverity.MEDIUM,
          { retryable: true }
        )
      case 500:
      case 502:
      case 503:
      case 504:
        throw new ApplicationError(
          userFriendlyMessage,
          ErrorCode.INTERNAL_ERROR,
          response.status,
          ErrorSeverity.HIGH,
          { retryable: true }
        )
      default:
        throw new ApplicationError(
          userFriendlyMessage,
          errorData.code || ErrorCode.UNKNOWN_ERROR,
          response.status,
          ErrorSeverity.MEDIUM
        )
    }
  }

  /**
   * Handle errors with toast notifications and logging
   */
  handleError(
    error: any,
    context: string,
    options: ErrorHandlingOptions = {}
  ): string {
    const {
      showToast = true,
      toastType = 'error',
      logError = true,
      reportError = false,
      retryCallback,
      fallbackMessage,
      context: additionalContext,
    } = options

    // Extract error information
    const errorInfo = this.extractErrorInfo(error)
    const userMessage = this.getUserFriendlyMessage(error, fallbackMessage)

    // Create error context
    const errorContext: ErrorContext = {
      route: typeof window !== 'undefined' ? window.location.pathname : undefined,
      timestamp: new Date().toISOString(),
      userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : undefined,
      ...additionalContext,
    }

    // Log error if requested
    if (logError) {
      console.error(`${context}:`, {
        error,
        errorInfo,
        context: errorContext,
      })
    }

    // Report error if requested (implement your error reporting service here)
    if (reportError) {
      this.reportError(error, context, errorContext)
    }

    // Show toast notification if requested
    if (showToast) {
      const toastOptions = retryCallback
        ? {
            action: {
              label: 'Retry',
              onClick: retryCallback,
            },
          }
        : undefined

      if (toastType === 'error') {
        toastService.error(userMessage, toastOptions)
      } else {
        toastService.warning(userMessage, toastOptions)
      }
    }

    return userMessage
  }

  /**
   * Wrapper for fetch with automatic error handling
   */
  async fetchWithErrorHandling(
    url: string,
    options: RequestInit = {},
    context: string = 'API call'
  ): Promise<any> {
    try {
      const response = await fetch(url, options)
      return await this.handleAPIResponse(response)
    } catch (error) {
      if (error instanceof ApplicationError) {
        throw error
      }

      // Handle network errors
      if (error instanceof TypeError && error.message.includes('fetch')) {
        throw new NetworkError('Network error. Please check your internet connection.')
      }

      // Log context for debugging
      console.error(`${context} failed:`, error)

      throw new ApplicationError(
        'An unexpected error occurred. Please try again.',
        ErrorCode.UNKNOWN_ERROR,
        500,
        ErrorSeverity.HIGH,
        { originalError: error as Error }
      )
    }
  }

  /**
   * Report error to external service (implement based on your needs)
   */
  private reportError(error: any, context: string, errorContext: ErrorContext): void {
    // Implement error reporting to services like Sentry, LogRocket, etc.
    console.log('Error reported:', { error, context, errorContext })
  }

  /**
   * Create standardized API error response
   */
  createAPIErrorResponse(
    error: ApplicationError,
    includeStack: boolean = false
  ): APIErrorResponse {
    return {
      success: false,
      error: error.message,
      code: error.code,
      statusCode: error.statusCode,
      details: includeStack ? { stack: error.stack } : undefined,
      timestamp: new Date().toISOString(),
    }
  }
}

// Export singleton instance
export const errorService = new ErrorService()

// Export convenience methods
export const handleError = errorService.handleError.bind(errorService)
export const handleAPIResponse = errorService.handleAPIResponse.bind(errorService)
export const fetchWithErrorHandling = errorService.fetchWithErrorHandling.bind(errorService)
