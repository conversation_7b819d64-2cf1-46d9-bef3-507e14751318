import { errorService, handleAPIResponse } from './error.service'
import { toastService } from './toast.service'
import { ApplicationError, ErrorCode } from '@/lib/types/error.types'

/**
 * API request configuration
 */
export interface APIRequestConfig extends RequestInit {
  showErrorToast?: boolean
  showSuccessToast?: boolean
  successMessage?: string
  retryCallback?: () => void
  context?: string
}

/**
 * API response wrapper
 */
export interface APIResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
  code?: string
  timestamp?: string
}

/**
 * Base API service class with centralized error handling and toast integration
 */
class BaseAPIService {
  private baseURL: string
  private defaultHeaders: HeadersInit

  constructor(baseURL: string = '/api') {
    this.baseURL = baseURL
    this.defaultHeaders = {
      'Content-Type': 'application/json',
    }
  }

  /**
   * Build full URL from endpoint
   */
  private buildURL(endpoint: string): string {
    // Remove leading slash from endpoint if present
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint
    // Ensure baseURL doesn't end with slash
    const cleanBaseURL = this.baseURL.endsWith('/') ? this.baseURL.slice(0, -1) : this.baseURL
    return `${cleanBaseURL}/${cleanEndpoint}`
  }

  /**
   * Prepare request headers
   */
  private prepareHeaders(config: APIRequestConfig): HeadersInit {
    return {
      ...this.defaultHeaders,
      ...config.headers,
    }
  }

  /**
   * Core request method with error handling and toast integration
   */
  private async request<T>(
    endpoint: string,
    config: APIRequestConfig = {}
  ): Promise<T> {
    const {
      showErrorToast = true,
      showSuccessToast = false,
      successMessage,
      retryCallback,
      context = `API request to ${endpoint}`,
      ...requestConfig
    } = config

    const url = this.buildURL(endpoint)
    const headers = this.prepareHeaders(config)

    try {
      const response = await fetch(url, {
        ...requestConfig,
        headers,
      })

      const data = await handleAPIResponse(response)

      // Show success toast if requested
      if (showSuccessToast && successMessage) {
        toastService.success(successMessage)
      }

      return data
    } catch (error) {
      // Handle error with toast and logging
      errorService.handleError(error, context, {
        showToast: showErrorToast,
        retryCallback,
      })

      // Re-throw the error so calling code can handle it if needed
      throw error
    }
  }

  /**
   * GET request
   */
  async get<T>(endpoint: string, config: APIRequestConfig = {}): Promise<T> {
    return this.request<T>(endpoint, {
      ...config,
      method: 'GET',
    })
  }

  /**
   * POST request
   */
  async post<T>(
    endpoint: string,
    data?: any,
    config: APIRequestConfig = {}
  ): Promise<T> {
    return this.request<T>(endpoint, {
      ...config,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  /**
   * PUT request
   */
  async put<T>(
    endpoint: string,
    data?: any,
    config: APIRequestConfig = {}
  ): Promise<T> {
    return this.request<T>(endpoint, {
      ...config,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  /**
   * PATCH request
   */
  async patch<T>(
    endpoint: string,
    data?: any,
    config: APIRequestConfig = {}
  ): Promise<T> {
    return this.request<T>(endpoint, {
      ...config,
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  /**
   * DELETE request
   */
  async delete<T>(endpoint: string, config: APIRequestConfig = {}): Promise<T> {
    return this.request<T>(endpoint, {
      ...config,
      method: 'DELETE',
    })
  }

  /**
   * Upload file with progress tracking
   */
  async uploadFile<T>(
    endpoint: string,
    file: File,
    additionalData?: Record<string, any>,
    config: APIRequestConfig = {}
  ): Promise<T> {
    const formData = new FormData()
    formData.append('file', file)

    // Add additional data to form
    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        formData.append(key, typeof value === 'string' ? value : JSON.stringify(value))
      })
    }

    const url = this.buildURL(endpoint)

    try {
      const response = await fetch(url, {
        method: 'POST',
        body: formData,
        // Don't set Content-Type header for FormData - browser will set it with boundary
        headers: {
          ...config.headers,
        },
      })

      const data = await handleAPIResponse(response)

      // Show success toast if requested
      if (config.showSuccessToast && config.successMessage) {
        toastService.success(config.successMessage)
      }

      return data
    } catch (error) {
      // Handle error with toast and logging
      errorService.handleError(error, `File upload to ${endpoint}`, {
        showToast: config.showErrorToast ?? true,
        retryCallback: config.retryCallback,
      })

      throw error
    }
  }

  /**
   * Download file
   */
  async downloadFile(
    endpoint: string,
    filename?: string,
    config: APIRequestConfig = {}
  ): Promise<void> {
    const url = this.buildURL(endpoint)

    try {
      const response = await fetch(url, {
        ...config,
        headers: this.prepareHeaders(config),
      })

      if (!response.ok) {
        await handleAPIResponse(response) // This will throw an appropriate error
      }

      const blob = await response.blob()
      const downloadUrl = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = filename || 'download'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(downloadUrl)

      if (config.showSuccessToast && config.successMessage) {
        toastService.success(config.successMessage)
      }
    } catch (error) {
      errorService.handleError(error, `File download from ${endpoint}`, {
        showToast: config.showErrorToast ?? true,
        retryCallback: config.retryCallback,
      })

      throw error
    }
  }

  /**
   * Batch requests with error handling
   */
  async batch<T>(
    requests: Array<{
      endpoint: string
      config?: APIRequestConfig
    }>,
    options: {
      failFast?: boolean
      showProgressToast?: boolean
    } = {}
  ): Promise<Array<T | Error>> {
    const { failFast = false, showProgressToast = false } = options

    if (showProgressToast) {
      toastService.loading(`Processing ${requests.length} requests...`)
    }

    const results: Array<T | Error> = []

    for (let i = 0; i < requests.length; i++) {
      const { endpoint, config = {} } = requests[i]

      try {
        const result = await this.get<T>(endpoint, {
          ...config,
          showErrorToast: false, // Handle errors at batch level
        })
        results.push(result)
      } catch (error) {
        results.push(error as Error)

        if (failFast) {
          if (showProgressToast) {
            toastService.dismiss()
            toastService.error(`Batch request failed at item ${i + 1}`)
          }
          throw error
        }
      }
    }

    if (showProgressToast) {
      toastService.dismiss()
      const errorCount = results.filter(r => r instanceof Error).length
      const successCount = results.length - errorCount

      if (errorCount === 0) {
        toastService.success(`All ${requests.length} requests completed successfully`)
      } else {
        toastService.warning(`${successCount} succeeded, ${errorCount} failed`)
      }
    }

    return results
  }
}

// Export singleton instance
export const apiService = new BaseAPIService()

// Export class for creating custom instances
export { BaseAPIService }
