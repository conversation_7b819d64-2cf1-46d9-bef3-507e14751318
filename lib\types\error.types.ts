/**
 * Standard error codes used throughout the application
 */
export enum ErrorCode {
  // Authentication & Authorization
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  SESSION_EXPIRED = 'SESSION_EXPIRED',
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',

  // Validation
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  INVALID_INPUT = 'INVALID_INPUT',
  MISSING_REQUIRED_FIELD = 'MISSING_REQUIRED_FIELD',

  // Resource Management
  NOT_FOUND = 'NOT_FOUND',
  ALREADY_EXISTS = 'ALREADY_EXISTS',
  CONFLICT = 'CONFLICT',
  RESOURCE_LOCKED = 'RESOURCE_LOCKED',

  // Rate Limiting & Quotas
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  QUOTA_EXCEEDED = 'QUOTA_EXCEEDED',
  MAX_ATTEMPTS_EXCEEDED = 'MAX_ATTEMPTS_EXCEEDED',

  // Server Errors
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  DATABASE_ERROR = 'DATABASE_ERROR',
  EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR',

  // Network & Connectivity
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  CONNECTION_ERROR = 'CONNECTION_ERROR',

  // Business Logic
  QUIZ_NOT_AVAILABLE = 'QUIZ_NOT_AVAILABLE',
  QUIZ_ALREADY_COMPLETED = 'QUIZ_ALREADY_COMPLETED',
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS',
  OPERATION_NOT_ALLOWED = 'OPERATION_NOT_ALLOWED',

  // File & Upload
  FILE_TOO_LARGE = 'FILE_TOO_LARGE',
  INVALID_FILE_TYPE = 'INVALID_FILE_TYPE',
  UPLOAD_FAILED = 'UPLOAD_FAILED',

  // Unknown
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

/**
 * Error severity levels
 */
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

/**
 * Error context information
 */
export interface ErrorContext {
  userId?: string
  userRole?: string
  route?: string
  component?: string
  action?: string
  timestamp?: string
  userAgent?: string
  additionalData?: Record<string, any>
}

/**
 * Base application error interface
 */
export interface AppError {
  message: string
  code: ErrorCode
  statusCode: number
  severity: ErrorSeverity
  context?: ErrorContext
  originalError?: Error
  stack?: string
  retryable?: boolean
  userFriendly?: boolean
}

/**
 * API Error response format
 */
export interface APIErrorResponse {
  success: false
  error: string
  code: ErrorCode
  statusCode: number
  details?: any
  timestamp: string
}

/**
 * Error handling options
 */
export interface ErrorHandlingOptions {
  showToast?: boolean
  toastType?: 'error' | 'warning'
  logError?: boolean
  reportError?: boolean
  retryCallback?: () => void
  fallbackMessage?: string
  context?: Partial<ErrorContext>
}

/**
 * Custom application error class
 */
export class ApplicationError extends Error implements AppError {
  public readonly code: ErrorCode
  public readonly statusCode: number
  public readonly severity: ErrorSeverity
  public readonly context?: ErrorContext
  public readonly originalError?: Error
  public readonly retryable: boolean
  public readonly userFriendly: boolean

  constructor(
    message: string,
    code: ErrorCode = ErrorCode.UNKNOWN_ERROR,
    statusCode: number = 500,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    options: {
      context?: ErrorContext
      originalError?: Error
      retryable?: boolean
      userFriendly?: boolean
    } = {}
  ) {
    super(message)
    this.name = 'ApplicationError'
    this.code = code
    this.statusCode = statusCode
    this.severity = severity
    this.context = options.context
    this.originalError = options.originalError
    this.retryable = options.retryable ?? false
    this.userFriendly = options.userFriendly ?? true

    // Maintain proper stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, ApplicationError)
    }
  }

  /**
   * Convert to JSON for logging/reporting
   */
  toJSON(): Record<string, any> {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      statusCode: this.statusCode,
      severity: this.severity,
      context: this.context,
      retryable: this.retryable,
      userFriendly: this.userFriendly,
      stack: this.stack,
      timestamp: new Date().toISOString(),
    }
  }

  /**
   * Create a user-friendly version of the error
   */
  toUserFriendly(): string {
    if (this.userFriendly) {
      return this.message
    }

    // Return generic message for non-user-friendly errors
    return 'An unexpected error occurred. Please try again.'
  }
}

/**
 * Network-specific error
 */
export class NetworkError extends ApplicationError {
  constructor(message: string = 'Network error occurred', originalError?: Error) {
    super(
      message,
      ErrorCode.NETWORK_ERROR,
      0,
      ErrorSeverity.HIGH,
      {
        originalError,
        retryable: true,
        userFriendly: true,
      }
    )
    this.name = 'NetworkError'
  }
}

/**
 * Validation-specific error
 */
export class ValidationError extends ApplicationError {
  public readonly validationErrors: Record<string, string[]>

  constructor(
    message: string = 'Validation failed',
    validationErrors: Record<string, string[]> = {},
    originalError?: Error
  ) {
    super(
      message,
      ErrorCode.VALIDATION_ERROR,
      400,
      ErrorSeverity.LOW,
      {
        originalError,
        retryable: false,
        userFriendly: true,
      }
    )
    this.name = 'ValidationError'
    this.validationErrors = validationErrors
  }

  toJSON(): Record<string, any> {
    return {
      ...super.toJSON(),
      validationErrors: this.validationErrors,
    }
  }
}

/**
 * Authentication-specific error
 */
export class AuthenticationError extends ApplicationError {
  constructor(message: string = 'Authentication required', originalError?: Error) {
    super(
      message,
      ErrorCode.UNAUTHORIZED,
      401,
      ErrorSeverity.MEDIUM,
      {
        originalError,
        retryable: false,
        userFriendly: true,
      }
    )
    this.name = 'AuthenticationError'
  }
}

/**
 * Authorization-specific error
 */
export class AuthorizationError extends ApplicationError {
  constructor(message: string = 'Insufficient permissions', originalError?: Error) {
    super(
      message,
      ErrorCode.FORBIDDEN,
      403,
      ErrorSeverity.MEDIUM,
      {
        originalError,
        retryable: false,
        userFriendly: true,
      }
    )
    this.name = 'AuthorizationError'
  }
}
