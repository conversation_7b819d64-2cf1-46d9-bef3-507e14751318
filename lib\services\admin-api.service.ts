import { BaseAPIService, APIRequestConfig } from './api.service'

/**
 * Admin-specific data types
 */
export interface AdminUser {
  id: string
  name: string
  email: string
  role: 'STUDENT' | 'ADMIN'
  bio?: string
  image?: string
  points: number
  level: number
  createdAt: string
  updatedAt: string
  lastLoginAt?: string
  isActive: boolean
}

export interface AdminQuiz {
  id: string
  title: string
  description?: string
  subject?: string
  difficulty: 'EASY' | 'MEDIUM' | 'HARD'
  questionCount: number
  timeLimit?: number
  isPublished: boolean
  createdAt: string
  updatedAt: string
  createdBy: string
  attempts: number
  averageScore: number
  completionRate: number
}

export interface QuizQuestion {
  id: string
  type: 'MCQ' | 'TRUE_FALSE' | 'FILL_BLANK' | 'ESSAY'
  text: string
  options?: string[]
  correctAnswer?: string
  explanation?: string
  points: number
  difficulty?: 'EASY' | 'MEDIUM' | 'HARD'
  tags?: string[]
  imageUrl?: string
  order?: number
}

export interface AdminDashboardStats {
  totalUsers: number
  totalQuizzes: number
  totalAttempts: number
  averageScore: number
  activeUsers: number
  recentActivity: any[]
  popularQuizzes: AdminQuiz[]
  userGrowth: Array<{ date: string; count: number }>
  quizPerformance: Array<{ quizId: string; title: string; averageScore: number; attempts: number }>
}

export interface SystemSettings {
  system: {
    siteName: string
    siteDescription: string
    siteUrl: string
    adminEmail: string
    allowRegistration: boolean
    requireEmailVerification: boolean
    defaultUserRole: string
    maxFileSize: number
    allowedFileTypes: string[]
    enableNotifications: boolean
    enableAnalytics: boolean
    maintenanceMode: boolean
    theme: string
    timezone: string
    language: string
  }
  security: {
    sessionTimeout: number
    maxLoginAttempts: number
    passwordMinLength: number
    requireStrongPassword: boolean
    enableTwoFactor: boolean
  }
  email: {
    provider: string
    smtpHost?: string
    smtpPort?: number
    smtpUser?: string
    smtpPassword?: string
    fromEmail: string
    fromName: string
  }
  ai: {
    provider: string
    apiKey?: string
    model?: string
    maxTokens?: number
    temperature?: number
  }
}

/**
 * Admin API Service
 * Handles all admin-related API calls with consistent error handling and toast notifications
 */
class AdminAPIService extends BaseAPIService {
  constructor() {
    super('/api/admin')
  }

  /**
   * Dashboard APIs
   */
  async getDashboardStats(): Promise<AdminDashboardStats> {
    return this.get<AdminDashboardStats>('/dashboard', {
      context: 'Loading admin dashboard',
      showErrorToast: true,
    })
  }

  /**
   * User management APIs
   */
  async getUsers(filters?: {
    search?: string
    role?: string
    page?: number
    limit?: number
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
  }): Promise<{ users: AdminUser[]; total: number; page: number; totalPages: number }> {
    const params = new URLSearchParams()
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== '') {
          params.append(key, value.toString())
        }
      })
    }

    const endpoint = params.toString() ? `/users?${params.toString()}` : '/users'
    
    return this.get(endpoint, {
      context: 'Loading users',
      showErrorToast: true,
    })
  }

  async createUser(userData: {
    name: string
    email: string
    role: 'STUDENT' | 'ADMIN'
    bio?: string
  }): Promise<AdminUser> {
    return this.post<AdminUser>('/users', userData, {
      context: 'Creating user',
      showErrorToast: true,
      showSuccessToast: true,
      successMessage: 'User created successfully!',
    })
  }

  async updateUser(userId: string, userData: Partial<AdminUser>): Promise<AdminUser> {
    return this.put<AdminUser>(`/users/${userId}`, userData, {
      context: 'Updating user',
      showErrorToast: true,
      showSuccessToast: true,
      successMessage: 'User updated successfully!',
    })
  }

  async deleteUser(userId: string): Promise<void> {
    return this.delete(`/users/${userId}`, {
      context: 'Deleting user',
      showErrorToast: true,
      showSuccessToast: true,
      successMessage: 'User deleted successfully!',
    })
  }

  async bulkDeleteUsers(userIds: string[]): Promise<void> {
    return this.post('/users/bulk-delete', { userIds }, {
      context: 'Bulk deleting users',
      showErrorToast: true,
      showSuccessToast: true,
      successMessage: `${userIds.length} users deleted successfully!`,
    })
  }

  /**
   * Quiz management APIs
   */
  async getQuizzes(filters?: {
    search?: string
    subject?: string
    difficulty?: string
    isPublished?: boolean
    page?: number
    limit?: number
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
  }): Promise<{ quizzes: AdminQuiz[]; total: number; page: number; totalPages: number }> {
    const params = new URLSearchParams()
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== '') {
          params.append(key, value.toString())
        }
      })
    }

    const endpoint = params.toString() ? `/quizzes?${params.toString()}` : '/quizzes'
    
    return this.get(endpoint, {
      context: 'Loading quizzes',
      showErrorToast: true,
    })
  }

  async getQuizDetails(quizId: string): Promise<AdminQuiz & { questions: QuizQuestion[] }> {
    return this.get<AdminQuiz & { questions: QuizQuestion[] }>(`/quizzes/${quizId}`, {
      context: 'Loading quiz details',
      showErrorToast: true,
    })
  }

  async createQuiz(quizData: {
    title: string
    description?: string
    subject?: string
    difficulty: 'EASY' | 'MEDIUM' | 'HARD'
    timeLimit?: number
    questions: Omit<QuizQuestion, 'id'>[]
  }): Promise<AdminQuiz> {
    return this.post<AdminQuiz>('/quizzes', quizData, {
      context: 'Creating quiz',
      showErrorToast: true,
      showSuccessToast: true,
      successMessage: 'Quiz created successfully!',
    })
  }

  async updateQuiz(quizId: string, quizData: Partial<AdminQuiz>): Promise<AdminQuiz> {
    return this.put<AdminQuiz>(`/quizzes/${quizId}`, quizData, {
      context: 'Updating quiz',
      showErrorToast: true,
      showSuccessToast: true,
      successMessage: 'Quiz updated successfully!',
    })
  }

  async deleteQuiz(quizId: string): Promise<void> {
    return this.delete(`/quizzes/${quizId}`, {
      context: 'Deleting quiz',
      showErrorToast: true,
      showSuccessToast: true,
      successMessage: 'Quiz deleted successfully!',
    })
  }

  async bulkDeleteQuizzes(quizIds: string[]): Promise<void> {
    return this.post('/quizzes/bulk-delete', { quizIds }, {
      context: 'Bulk deleting quizzes',
      showErrorToast: true,
      showSuccessToast: true,
      successMessage: `${quizIds.length} quizzes deleted successfully!`,
    })
  }

  async publishQuiz(quizId: string): Promise<void> {
    return this.post(`/quizzes/${quizId}/publish`, undefined, {
      context: 'Publishing quiz',
      showErrorToast: true,
      showSuccessToast: true,
      successMessage: 'Quiz published successfully!',
    })
  }

  async unpublishQuiz(quizId: string): Promise<void> {
    return this.post(`/quizzes/${quizId}/unpublish`, undefined, {
      context: 'Unpublishing quiz',
      showErrorToast: true,
      showSuccessToast: true,
      successMessage: 'Quiz unpublished successfully!',
    })
  }

  /**
   * Analytics APIs
   */
  async getQuizAnalytics(quizId: string, period?: string): Promise<{
    attempts: number
    completions: number
    averageScore: number
    averageTime: number
    questionAnalytics: Array<{
      questionId: string
      correctRate: number
      averageTime: number
    }>
    timeSeriesData: Array<{ date: string; attempts: number; averageScore: number }>
  }> {
    const params = period ? `?period=${period}` : ''
    return this.get(`/analytics/quiz/${quizId}${params}`, {
      context: 'Loading quiz analytics',
      showErrorToast: true,
    })
  }

  async getUserAnalytics(userId: string): Promise<{
    totalAttempts: number
    completedQuizzes: number
    averageScore: number
    totalTimeSpent: number
    subjectPerformance: Array<{ subject: string; averageScore: number; attempts: number }>
    progressOverTime: Array<{ date: string; score: number }>
  }> {
    return this.get(`/analytics/user/${userId}`, {
      context: 'Loading user analytics',
      showErrorToast: true,
    })
  }

  /**
   * Settings APIs
   */
  async getSettings(): Promise<SystemSettings> {
    return this.get<SystemSettings>('/settings', {
      context: 'Loading system settings',
      showErrorToast: true,
    })
  }

  async updateSettings(settings: Partial<SystemSettings>): Promise<SystemSettings> {
    return this.put<SystemSettings>('/settings', settings, {
      context: 'Updating system settings',
      showErrorToast: true,
      showSuccessToast: true,
      successMessage: 'Settings updated successfully!',
    })
  }

  /**
   * Notification APIs
   */
  async sendNotification(data: {
    title: string
    message: string
    type: 'info' | 'success' | 'warning' | 'error'
    recipients: 'all' | 'students' | 'admins' | string[]
    scheduledFor?: string
  }): Promise<void> {
    return this.post('/notifications/send', data, {
      context: 'Sending notification',
      showErrorToast: true,
      showSuccessToast: true,
      successMessage: 'Notification sent successfully!',
    })
  }

  async getNotificationHistory(filters?: {
    page?: number
    limit?: number
    type?: string
  }): Promise<{ notifications: any[]; total: number; page: number; totalPages: number }> {
    const params = new URLSearchParams()
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== '') {
          params.append(key, value.toString())
        }
      })
    }

    const endpoint = params.toString() ? `/notifications?${params.toString()}` : '/notifications'
    
    return this.get(endpoint, {
      context: 'Loading notification history',
      showErrorToast: true,
    })
  }

  /**
   * Export APIs
   */
  async exportUsers(format: 'csv' | 'xlsx' = 'csv'): Promise<void> {
    return this.downloadFile(`/export/users?format=${format}`, `users.${format}`, {
      context: 'Exporting users',
      showErrorToast: true,
      showSuccessToast: true,
      successMessage: 'Users exported successfully!',
    })
  }

  async exportQuizzes(format: 'csv' | 'xlsx' = 'csv'): Promise<void> {
    return this.downloadFile(`/export/quizzes?format=${format}`, `quizzes.${format}`, {
      context: 'Exporting quizzes',
      showErrorToast: true,
      showSuccessToast: true,
      successMessage: 'Quizzes exported successfully!',
    })
  }

  async exportQuizResults(quizId: string, format: 'csv' | 'xlsx' = 'csv'): Promise<void> {
    return this.downloadFile(`/export/quiz/${quizId}/results?format=${format}`, `quiz-results.${format}`, {
      context: 'Exporting quiz results',
      showErrorToast: true,
      showSuccessToast: true,
      successMessage: 'Quiz results exported successfully!',
    })
  }
}

// Export singleton instance
export const adminApiService = new AdminAPIService()

// Export class for testing or custom instances
export { AdminAPIService }
