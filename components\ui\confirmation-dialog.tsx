"use client"

import * as React from "react"
import { useState } from "react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Button } from "@/components/ui/button"
import { Loader2, AlertTriangle, Info, CheckCircle, HelpCircle } from "lucide-react"
import { cn } from "@/lib/utils"

interface ConfirmationDialogProps {
  trigger: React.ReactNode
  title: string
  description: string
  confirmText?: string
  cancelText?: string
  variant?: "default" | "destructive"
  onConfirm: () => Promise<void> | void
  disabled?: boolean
}

export function ConfirmationDialog({
  trigger,
  title,
  description,
  confirmText = "Confirm",
  cancelText = "Cancel",
  variant = "default",
  onConfirm,
  disabled = false
}: ConfirmationDialogProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const handleConfirm = async () => {
    try {
      setIsLoading(true)
      await onConfirm()
      setIsOpen(false)
    } catch (error) {
      console.error('Confirmation action failed:', error)
      // Keep dialog open on error so user can retry
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
      <AlertDialogTrigger asChild disabled={disabled}>
        {trigger}
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{title}</AlertDialogTitle>
          <AlertDialogDescription>
            {description}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isLoading}>
            {cancelText}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            disabled={isLoading}
            className={variant === "destructive" ? "bg-destructive text-destructive-foreground hover:bg-destructive/90" : ""}
          >
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {confirmText}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}

// Specialized delete confirmation dialog
interface DeleteConfirmationDialogProps {
  trigger: React.ReactNode
  itemName: string
  itemType?: string
  onDelete: () => Promise<void> | void
  disabled?: boolean
}

export function DeleteConfirmationDialog({
  trigger,
  itemName,
  itemType = "item",
  onDelete,
  disabled = false
}: DeleteConfirmationDialogProps) {
  return (
    <ConfirmationDialog
      trigger={trigger}
      title={`Delete ${itemType}`}
      description={`Are you sure you want to delete "${itemName}"? This action cannot be undone.`}
      confirmText="Delete"
      cancelText="Cancel"
      variant="destructive"
      onConfirm={onDelete}
      disabled={disabled}
    />
  )
}

// Bulk action confirmation dialog
interface BulkActionDialogProps {
  trigger: React.ReactNode
  action: string
  itemCount: number
  itemType?: string
  onConfirm: () => Promise<void> | void
  disabled?: boolean
}

export function BulkActionDialog({
  trigger,
  action,
  itemCount,
  itemType = "items",
  onConfirm,
  disabled = false
}: BulkActionDialogProps) {
  const getDescription = () => {
    if (action.toLowerCase().includes('delete')) {
      return `Are you sure you want to ${action.toLowerCase()} ${itemCount} ${itemType}? This action cannot be undone.`
    }
    return `Are you sure you want to ${action.toLowerCase()} ${itemCount} ${itemType}?`
  }

  return (
    <ConfirmationDialog
      trigger={trigger}
      title={`${action} ${itemType}`}
      description={getDescription()}
      confirmText={action}
      cancelText="Cancel"
      variant={action.toLowerCase().includes('delete') ? "destructive" : "default"}
      onConfirm={onConfirm}
      disabled={disabled}
    />
  )
}

// Export confirmation dialog with progress
interface ExportConfirmationDialogProps {
  trigger: React.ReactNode
  exportType: string
  itemCount?: number
  onConfirm: () => Promise<void> | void
  disabled?: boolean
}

export function ExportConfirmationDialog({
  trigger,
  exportType,
  itemCount,
  onConfirm,
  disabled = false
}: ExportConfirmationDialogProps) {
  const getDescription = () => {
    if (itemCount) {
      return `This will export ${itemCount} ${exportType} items. The export will be processed in the background and you'll be notified when it's ready for download.`
    }
    return `This will create a ${exportType} export. The export will be processed in the background and you'll be notified when it's ready for download.`
  }

  return (
    <ConfirmationDialog
      trigger={trigger}
      title={`Export ${exportType}`}
      description={getDescription()}
      confirmText="Start Export"
      cancelText="Cancel"
      variant="default"
      onConfirm={onConfirm}
      disabled={disabled}
    />
  )
}

// Hook-based confirmation for replacing browser confirm()
export type ConfirmationVariant = 'default' | 'destructive' | 'warning' | 'info' | 'success'

export interface SimpleConfirmationProps {
  title: string
  description: string
  confirmText?: string
  cancelText?: string
  variant?: ConfirmationVariant
  onConfirm: () => void | Promise<void>
  onCancel?: () => void
}

const variantConfig = {
  default: {
    icon: HelpCircle,
    iconClassName: "text-muted-foreground",
  },
  destructive: {
    icon: AlertTriangle,
    iconClassName: "text-destructive",
  },
  warning: {
    icon: AlertTriangle,
    iconClassName: "text-yellow-500",
  },
  info: {
    icon: Info,
    iconClassName: "text-blue-500",
  },
  success: {
    icon: CheckCircle,
    iconClassName: "text-green-500",
  },
}

export function useConfirmationDialog() {
  const [dialogProps, setDialogProps] = React.useState<SimpleConfirmationProps | null>(null)
  const [isLoading, setIsLoading] = React.useState(false)

  const confirm = React.useCallback((props: SimpleConfirmationProps) => {
    return new Promise<boolean>((resolve) => {
      setDialogProps({
        ...props,
        onConfirm: async () => {
          try {
            setIsLoading(true)
            await props.onConfirm()
            resolve(true)
            setDialogProps(null)
          } catch (error) {
            resolve(false)
            throw error
          } finally {
            setIsLoading(false)
          }
        },
        onCancel: () => {
          props.onCancel?.()
          resolve(false)
          setDialogProps(null)
        },
      })
    })
  }, [])

  const ConfirmationDialogComponent = React.useCallback(() => {
    if (!dialogProps) return null

    const config = variantConfig[dialogProps.variant || 'default']
    const IconComponent = config.icon

    return (
      <AlertDialog
        open={!!dialogProps}
        onOpenChange={(open) => {
          if (!open && !isLoading) {
            dialogProps.onCancel?.()
            setDialogProps(null)
          }
        }}
      >
        <AlertDialogContent className="sm:max-w-[425px]">
          <AlertDialogHeader>
            <div className="flex items-center gap-3">
              <IconComponent className={cn("h-5 w-5 flex-shrink-0", config.iconClassName)} />
              <AlertDialogTitle className="text-left">{dialogProps.title}</AlertDialogTitle>
            </div>
            <AlertDialogDescription className="text-left">
              {dialogProps.description}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2">
            <AlertDialogCancel
              onClick={() => {
                if (!isLoading) {
                  dialogProps.onCancel?.()
                  setDialogProps(null)
                }
              }}
              disabled={isLoading}
            >
              {dialogProps.cancelText || "Cancel"}
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={dialogProps.onConfirm}
              disabled={isLoading}
              className={dialogProps.variant === "destructive" ? "bg-destructive text-destructive-foreground hover:bg-destructive/90" : ""}
            >
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {dialogProps.confirmText || "Confirm"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    )
  }, [dialogProps, isLoading])

  return {
    confirm,
    ConfirmationDialog: ConfirmationDialogComponent,
  }
}

// Context-based confirmation system
export interface ConfirmationContextValue {
  confirm: (props: SimpleConfirmationProps) => Promise<boolean>
}

const ConfirmationContext = React.createContext<ConfirmationContextValue | null>(null)

export function ConfirmationProvider({ children }: { children: React.ReactNode }) {
  const { confirm, ConfirmationDialog } = useConfirmationDialog()

  return (
    <ConfirmationContext.Provider value={{ confirm }}>
      {children}
      <ConfirmationDialog />
    </ConfirmationContext.Provider>
  )
}

export function useConfirmation() {
  const context = React.useContext(ConfirmationContext)
  if (!context) {
    throw new Error('useConfirmation must be used within a ConfirmationProvider')
  }
  return context
}

// Convenience hook for common confirmation patterns
export function useCommonConfirmations() {
  const { confirm } = useConfirmation()

  const confirmDelete = React.useCallback(
    (itemName: string, onConfirm: () => void | Promise<void>) => {
      return confirm({
        title: "Delete Confirmation",
        description: `Are you sure you want to delete "${itemName}"? This action cannot be undone.`,
        confirmText: "Delete",
        cancelText: "Cancel",
        variant: "destructive",
        onConfirm,
      })
    },
    [confirm]
  )

  const confirmBulkDelete = React.useCallback(
    (count: number, onConfirm: () => void | Promise<void>) => {
      return confirm({
        title: "Bulk Delete Confirmation",
        description: `Are you sure you want to delete ${count} item(s)? This action cannot be undone.`,
        confirmText: "Delete All",
        cancelText: "Cancel",
        variant: "destructive",
        onConfirm,
      })
    },
    [confirm]
  )

  const confirmUnsavedChanges = React.useCallback(
    (onConfirm: () => void | Promise<void>) => {
      return confirm({
        title: "Unsaved Changes",
        description: "You have unsaved changes. Are you sure you want to leave? Your changes will be lost.",
        confirmText: "Leave",
        cancelText: "Stay",
        variant: "warning",
        onConfirm,
      })
    },
    [confirm]
  )

  const confirmAction = React.useCallback(
    (
      title: string,
      description: string,
      onConfirm: () => void | Promise<void>,
      variant: ConfirmationVariant = "default"
    ) => {
      return confirm({
        title,
        description,
        variant,
        onConfirm,
      })
    },
    [confirm]
  )

  return {
    confirmDelete,
    confirmBulkDelete,
    confirmUnsavedChanges,
    confirmAction,
  }
}
