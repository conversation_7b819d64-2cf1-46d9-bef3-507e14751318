import { toast as sonnerToast } from "sonner"

/**
 * Toast types supported by the system
 */
export type ToastType = 'success' | 'error' | 'warning' | 'info' | 'loading' | 'default'

/**
 * Toast options interface
 */
export interface ToastOptions {
  description?: string
  duration?: number
  action?: {
    label: string
    onClick: () => void
  }
  cancel?: {
    label: string
    onClick?: () => void
  }
  id?: string | number
  dismissible?: boolean
  closeButton?: boolean
}

/**
 * Internal toast tracking for deduplication
 */
interface ToastTracker {
  message: string
  type: ToastType
  timestamp: number
}

class ToastService {
  private recentToasts = new Map<string, ToastTracker>()
  private readonly DEDUPLICATION_WINDOW = 3000 // 3 seconds
  private readonly MAX_CONCURRENT_TOASTS = 5

  /**
   * Safely convert any value to a string for toast display
   */
  private safeMessage(message: any): string {
    // Handle Error instances (including custom errors)
    if (message instanceof Error) {
      return message.message || 'An error occurred'
    }

    if (typeof message === 'string') {
      return message.trim() || 'No message provided'
    }

    if (typeof message === 'number') {
      return message.toString()
    }

    if (typeof message === 'boolean') {
      return message.toString()
    }

    // Handle objects with message property
    if (message && typeof message === 'object') {
      if ('message' in message && typeof message.message === 'string') {
        return message.message.trim() || 'No message provided'
      }
      
      if ('error' in message && typeof message.error === 'string') {
        return message.error.trim() || 'An error occurred'
      }

      // Try to stringify object, but avoid [object Object]
      try {
        const stringified = JSON.stringify(message)
        if (stringified && stringified !== '{}' && stringified !== 'null') {
          return stringified
        }
      } catch {
        // JSON.stringify failed, fall through to default
      }
    }

    // Fallback for null, undefined, or unparseable objects
    return 'An error occurred'
  }

  /**
   * Generate a unique key for toast deduplication
   */
  private getToastKey(message: string, type: ToastType): string {
    return `${type}:${message}`
  }

  /**
   * Check if a toast should be deduplicated
   */
  private shouldDeduplicate(message: string, type: ToastType): boolean {
    const key = this.getToastKey(message, type)
    const existing = this.recentToasts.get(key)
    
    if (!existing) {
      return false
    }

    const now = Date.now()
    const timeDiff = now - existing.timestamp

    // Remove expired entries
    if (timeDiff > this.DEDUPLICATION_WINDOW) {
      this.recentToasts.delete(key)
      return false
    }

    return true
  }

  /**
   * Track a toast for deduplication
   */
  private trackToast(message: string, type: ToastType): void {
    const key = this.getToastKey(message, type)
    this.recentToasts.set(key, {
      message,
      type,
      timestamp: Date.now()
    })

    // Clean up old entries periodically
    if (this.recentToasts.size > this.MAX_CONCURRENT_TOASTS * 2) {
      this.cleanupOldToasts()
    }
  }

  /**
   * Clean up old toast entries
   */
  private cleanupOldToasts(): void {
    const now = Date.now()
    for (const [key, tracker] of this.recentToasts.entries()) {
      if (now - tracker.timestamp > this.DEDUPLICATION_WINDOW) {
        this.recentToasts.delete(key)
      }
    }
  }

  /**
   * Process toast options to ensure they're safe
   */
  private processOptions(options?: ToastOptions): any {
    if (!options) return undefined

    return {
      ...options,
      description: options.description ? this.safeMessage(options.description) : undefined,
      duration: options.duration || 4000,
    }
  }

  /**
   * Show a toast notification
   */
  private showToast(type: ToastType, message: any, options?: ToastOptions): string | number {
    const safeMessage = this.safeMessage(message)
    
    // Check for deduplication
    if (this.shouldDeduplicate(safeMessage, type)) {
      console.log(`Toast deduplicated: ${type} - ${safeMessage}`)
      return 'deduplicated'
    }

    // Track this toast
    this.trackToast(safeMessage, type)

    const processedOptions = this.processOptions(options)

    // Show the appropriate toast type
    switch (type) {
      case 'success':
        return sonnerToast.success(safeMessage, processedOptions)
      case 'error':
        return sonnerToast.error(safeMessage, processedOptions)
      case 'warning':
        return sonnerToast.warning(safeMessage, processedOptions)
      case 'info':
        return sonnerToast.info(safeMessage, processedOptions)
      case 'loading':
        return sonnerToast.loading(safeMessage, processedOptions)
      default:
        return sonnerToast(safeMessage, processedOptions)
    }
  }

  /**
   * Show success toast
   */
  success(message: any, options?: ToastOptions): string | number {
    return this.showToast('success', message, options)
  }

  /**
   * Show error toast
   */
  error(message: any, options?: ToastOptions): string | number {
    return this.showToast('error', message, options)
  }

  /**
   * Show warning toast
   */
  warning(message: any, options?: ToastOptions): string | number {
    return this.showToast('warning', message, options)
  }

  /**
   * Show info toast
   */
  info(message: any, options?: ToastOptions): string | number {
    return this.showToast('info', message, options)
  }

  /**
   * Show loading toast
   */
  loading(message: any, options?: ToastOptions): string | number {
    return this.showToast('loading', message, options)
  }

  /**
   * Show default toast
   */
  default(message: any, options?: ToastOptions): string | number {
    return this.showToast('default', message, options)
  }

  /**
   * Dismiss a specific toast
   */
  dismiss(toastId?: string | number): void {
    sonnerToast.dismiss(toastId)
  }

  /**
   * Dismiss all toasts
   */
  dismissAll(): void {
    sonnerToast.dismiss()
  }

  /**
   * Promise-based toast for async operations
   */
  promise<T>(
    promise: Promise<T>,
    {
      loading,
      success,
      error,
    }: {
      loading: string
      success: string | ((data: T) => string)
      error: string | ((error: any) => string)
    }
  ): Promise<T> {
    return sonnerToast.promise(promise, {
      loading: this.safeMessage(loading),
      success: (data: T) => {
        const successMessage = typeof success === 'function' ? success(data) : success
        return this.safeMessage(successMessage)
      },
      error: (err: any) => {
        const errorMessage = typeof error === 'function' ? error(err) : error
        return this.safeMessage(errorMessage)
      },
    })
  }
}

// Export singleton instance
export const toastService = new ToastService()

// Export convenience methods for backward compatibility
export const toast = {
  success: (message: any, options?: ToastOptions) => toastService.success(message, options),
  error: (message: any, options?: ToastOptions) => toastService.error(message, options),
  warning: (message: any, options?: ToastOptions) => toastService.warning(message, options),
  info: (message: any, options?: ToastOptions) => toastService.info(message, options),
  loading: (message: any, options?: ToastOptions) => toastService.loading(message, options),
  default: (message: any, options?: ToastOptions) => toastService.default(message, options),
  dismiss: (toastId?: string | number) => toastService.dismiss(toastId),
  dismissAll: () => toastService.dismissAll(),
  promise: toastService.promise.bind(toastService),
}
